import * as React from "react";
import {Text, StyleSheet, Image, View} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const Frame49 = () => {
  	
  	return (
    		<SafeAreaView style={styles.viewBg}>
      			<View style={[styles.view, styles.viewBg]}>
        				<Text style={[styles.tue3Jun, styles.tue3JunTypo]}>Tue, 3 Jun</Text>
        				<View style={styles.frameParent}>
          					<View style={[styles.image8Parent, styles.parentFlexBox]}>
            						<Image style={styles.image8Icon} resizeMode="cover" source="image 8.png" />
            						<Text style={[styles.panthers, styles.tue3JunTypo]}>Panthers</Text>
          					</View>
          					<View style={[styles.group, styles.groupFlexBox]}>
            						<Text style={[styles.text, styles.textTypo1]}>4</Text>
            						<Text style={[styles.text1, styles.textTypo1]}>-</Text>
            						<Text style={[styles.text, styles.textTypo1]}>3</Text>
          					</View>
          					<View style={[styles.image8Parent, styles.parentFlexBox]}>
            						<Image style={styles.image9Icon} resizeMode="cover" source="image 9.png" />
            						<Text style={[styles.panthers, styles.tue3JunTypo]}>Oilers</Text>
          					</View>
        				</View>
        				<View style={[styles.lineParent, styles.parentFlexBox]}>
          					<View style={styles.frameChild} />
          					<View style={[styles.frameGroup, styles.groupFlexBox]}>
            						<View style={styles.teamParent}>
              							<Text style={[styles.team, styles.textTypo]}>Team</Text>
              							<Text style={[styles.floridaPanthers, styles.textTypo]}>Florida Panthers</Text>
              							<Text style={[styles.floridaPanthers, styles.textTypo]}>Edmonton Oilers</Text>
            						</View>
            						<View style={styles.frameContainer}>
              							<View style={styles.container}>
                								<Text style={[styles.text3, styles.textTypo]}>1</Text>
                								<Text style={[styles.text4, styles.textTypo]}>1</Text>
                								<Text style={[styles.text4, styles.textTypo]}>2</Text>
              							</View>
              							<View style={styles.teamParent}>
                								<Text style={[styles.team, styles.textTypo]}>2</Text>
                								<Text style={[styles.floridaPanthers, styles.textTypo]}>1</Text>
                								<Text style={[styles.floridaPanthers, styles.textTypo]}>1</Text>
              							</View>
              							<View style={styles.parent1}>
                								<Text style={[styles.text3, styles.textTypo]}>3</Text>
                								<Text style={[styles.text4, styles.textTypo]}>1</Text>
                								<Text style={[styles.text4, styles.textTypo]}>0</Text>
              							</View>
              							<View style={styles.parent1}>
                								<Text style={[styles.text3, styles.textTypo]}>OT</Text>
                								<Text style={[styles.text4, styles.textTypo]}>1</Text>
                								<Text style={[styles.text4, styles.textTypo]}>0</Text>
              							</View>
              							<View style={styles.parent1}>
                								<Text style={[styles.text3, styles.textTypo]}>T</Text>
                								<Text style={[styles.text4, styles.textTypo]}>4</Text>
                								<Text style={[styles.text4, styles.textTypo]}>3</Text>
              							</View>
            						</View>
          					</View>
          					<View style={styles.frameChild} />
        				</View>
      			</View>
    		</SafeAreaView>);
};

const styles = StyleSheet.create({
  	parent: {
    		flex: 1,
    		backgroundColor: "#fff"
  	},
  	viewBg: {
    		backgroundColor: "#fff",
    		flex: 1
  	},
  	tue3JunTypo: {
    		textAlign: "center",
    		fontFamily: "Archivo-Regular"
  	},
  	parentFlexBox: {
    		gap: 8,
    		justifyContent: "center",
    		alignItems: "center"
  	},
  	groupFlexBox: {
    		gap: 0,
    		justifyContent: "space-between",
    		flexDirection: "row",
    		alignItems: "center"
  	},
  	textTypo1: {
    		fontSize: 30,
    		color: "#231716",
    		textAlign: "center",
    		fontFamily: "Archivo-Regular"
  	},
  	textTypo: {
    		textAlign: "left",
    		fontFamily: "Archivo-Regular",
    		fontSize: 16
  	},
  	tue3Jun: {
    		color: "#7f7271",
    		fontSize: 16,
    		textAlign: "center",
    		fontFamily: "Archivo-Regular",
    		alignSelf: "stretch"
  	},
  	image8Icon: {
    		width: 40,
    		height: 45
  	},
  	panthers: {
    		fontSize: 22,
    		color: "#231716"
  	},
  	image8Parent: {
    		width: 60
  	},
  	text: {
    		width: 23
  	},
  	text1: {
    		width: 14,
    		display: "flex",
    		justifyContent: "center",
    		alignItems: "center"
  	},
  	group: {
    		width: 160
  	},
  	image9Icon: {
    		width: 45,
    		height: 45
  	},
  	frameParent: {
    		gap: 30,
    		flexDirection: "row",
    		alignSelf: "stretch"
  	},
  	frameChild: {
    		borderStyle: "solid",
    		borderColor: "#7f7271",
    		borderTopWidth: 0.5,
    		height: 1,
    		alignSelf: "stretch"
  	},
  	team: {
    		color: "#7f7271",
    		alignSelf: "stretch"
  	},
  	floridaPanthers: {
    		color: "#231716",
    		alignSelf: "stretch"
  	},
  	teamParent: {
    		gap: 10,
    		justifyContent: "center"
  	},
  	text3: {
    		color: "#7f7271"
  	},
  	text4: {
    		color: "#231716"
  	},
  	container: {
    		gap: 10
  	},
  	parent1: {
    		gap: 10,
    		justifyContent: "center",
    		alignItems: "center"
  	},
  	frameContainer: {
    		gap: 20,
    		flexDirection: "row",
    		alignItems: "center"
  	},
  	frameGroup: {
    		paddingHorizontal: 10,
    		paddingVertical: 0,
    		alignSelf: "stretch"
  	},
  	lineParent: {
    		width: 370,
    		paddingTop: 20
  	},
  	view: {
    		width: "100%",
    		shadowColor: "rgba(0, 0, 0, 0.05)",
    		shadowOffset: {
      			width: 0,
      			height: 0
    		},
    		shadowRadius: 8,
    		elevation: 8,
    		shadowOpacity: 1,
    		borderRadius: 5,
    		overflow: "hidden",
    		paddingHorizontal: 30,
    		paddingTop: 15,
    		paddingBottom: 20,
    		gap: 15,
    		justifyContent: "center",
    		alignItems: "center"
  	}
});

export default Frame49;
