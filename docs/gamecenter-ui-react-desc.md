import * as React from "react";
import {Text, StyleSheet, Image, View} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const FullGamecenter = () => {
  	
  	return (
    		<SafeAreaView style={styles.fullGamecenter}>
      			<View style={[styles.view, styles.viewFrameFlexBox]}>
        				<View style={[styles.tue3JunParent, styles.parentShadowBox]}>
          					<Text style={[styles.tue3Jun, styles.tue3JunTypo]}>Tue, 3 Jun</Text>
          					<View style={styles.frameParent}>
            						<View style={[styles.image8Parent, styles.parentFlexBox]}>
              							<Image style={styles.image8Icon} resizeMode="cover" source="image 8.png" />
              							<Text style={[styles.panthers, styles.tue3JunTypo]}>Panthers</Text>
            						</View>
            						<View style={[styles.parent, styles.parentFrameFlexBox]}>
              							<Text style={[styles.text, styles.textTypo1]}>4</Text>
              							<Text style={[styles.text1, styles.textTypo1]}>-</Text>
              							<Text style={[styles.text, styles.textTypo1]}>3</Text>
            						</View>
            						<View style={[styles.image8Parent, styles.parentFlexBox]}>
              							<Image style={styles.image9Icon} resizeMode="cover" source="image 9.png" />
              							<Text style={[styles.panthers, styles.tue3JunTypo]}>Oilers</Text>
            						</View>
          					</View>
          					<View style={[styles.lineParent, styles.parentFlexBox]}>
            						<View style={styles.frameChild} />
            						<View style={[styles.frameGroup, styles.parentFrameFlexBox]}>
              							<View style={styles.teamParent}>
                								<Text style={[styles.team, styles.textTypo]}>Team</Text>
                								<Text style={[styles.floridaPanthers, styles.textTypo]}>Florida Panthers</Text>
                								<Text style={[styles.floridaPanthers, styles.textTypo]}>Edmonton Oilers</Text>
              							</View>
              							<View style={[styles.frameContainer, styles.rosterParentFlexBox]}>
                								<View style={styles.group}>
                  									<Text style={[styles.text3, styles.textTypo]}>1</Text>
                  									<Text style={[styles.text4, styles.textTypo]}>1</Text>
                  									<Text style={[styles.text4, styles.textTypo]}>2</Text>
                								</View>
                								<View style={styles.teamParent}>
                  									<Text style={[styles.team, styles.textTypo]}>2</Text>
                  									<Text style={[styles.floridaPanthers, styles.textTypo]}>1</Text>
                  									<Text style={[styles.floridaPanthers, styles.textTypo]}>1</Text>
                								</View>
                								<View style={[styles.frameView, styles.viewFrameFlexBox]}>
                  									<Text style={[styles.text3, styles.textTypo]}>3</Text>
                  									<Text style={[styles.text4, styles.textTypo]}>1</Text>
                  									<Text style={[styles.text4, styles.textTypo]}>0</Text>
                								</View>
                								<View style={[styles.frameView, styles.viewFrameFlexBox]}>
                  									<Text style={[styles.text3, styles.textTypo]}>OT</Text>
                  									<Text style={[styles.text4, styles.textTypo]}>1</Text>
                  									<Text style={[styles.text4, styles.textTypo]}>0</Text>
                								</View>
                								<View style={[styles.frameView, styles.viewFrameFlexBox]}>
                  									<Text style={[styles.text3, styles.textTypo]}>T</Text>
                  									<Text style={[styles.text4, styles.textTypo]}>4</Text>
                  									<Text style={[styles.text4, styles.textTypo]}>3</Text>
                								</View>
              							</View>
            						</View>
            						<View style={styles.frameChild} />
          					</View>
        				</View>
        				<View style={[styles.rosterParent, styles.rosterParentFlexBox]}>
          					<Text style={styles.roster}>Roster</Text>
          					<View style={[styles.frameParent1, styles.viewFrameFlexBox]}>
            						<View style={[styles.frameParent2, styles.wrapperFlexBox]}>
              							<View style={[styles.oilersWrapper, styles.wrapperFlexBox]}>
                								<Text style={[styles.oilers1, styles.oilers1Typo]}>Oilers</Text>
              							</View>
              							<View style={[styles.panthersWrapper, styles.wrapperFlexBox]}>
                								<Text style={[styles.panthers1, styles.oilers1Typo]}>Panthers</Text>
              							</View>
            						</View>
            						<View style={styles.screenshot20250609At1052Parent}>
              							<Image style={[styles.screenshot20250609At1052, styles.wrapperFlexBox]} resizeMode="cover" source="Screenshot 2025-06-09 at 10.52.15 AM 1.png" />
              							<View style={styles.screenshot20250609At1052Group}>
                								<Image style={styles.screenshot20250609At10521} resizeMode="cover" source="Screenshot 2025-06-09 at 10.52.15 AM 2.png" />
                								<View style={styles.groupChild} />
                								<Text style={styles.goalie}>GOALIE</Text>
                								<View style={[styles.groupItem, styles.groupLayout]} />
                								<View style={[styles.groupInner, styles.groupLayout]} />
                								<Text style={[styles.sv, styles.svTypo]}>SV%</Text>
                								<Text style={[styles.gaa, styles.svTypo]}>GAA</Text>
                								<View style={[styles.rectangleView, styles.groupChild2Layout]} />
                								<View style={[styles.groupChild1, styles.groupChildLayout1]} />
                								<View style={[styles.groupChild2, styles.groupChild2Layout]} />
                								<View style={[styles.groupChild3, styles.groupChildLayout1]} />
                								<View style={[styles.lineView, styles.groupChildLayout]} />
                								<View style={[styles.groupChild4, styles.groupChildLayout]} />
                								<View style={[styles.groupChild5, styles.groupChildLayout]} />
                								<View style={[styles.groupChild6, styles.groupChildLayout]} />
              							</View>
            						</View>
          					</View>
        				</View>
        				<View style={[styles.gameInfoParent, styles.parentShadowBox]}>
          					<Text style={styles.gameInfo}>Game Info</Text>
          					<View style={[styles.frameParent3, styles.parentFrameFlexBox]}>
            						<View style={styles.refereeParent}>
              							<Text style={[styles.team, styles.textTypo]}>Referee</Text>
              							<Text style={[styles.team, styles.textTypo]}>EDM Coach</Text>
              							<Text style={[styles.team, styles.textTypo]}>FLA Coach</Text>
            						</View>
            						<View style={styles.danialUtegenovParent}>
              							<Text style={[styles.floridaPanthers, styles.textTypo]}>Danial Utegenov</Text>
              							<Text style={[styles.floridaPanthers, styles.textTypo]}>Solomon Kim</Text>
              							<Text style={[styles.floridaPanthers, styles.textTypo]}>Vlad Yun</Text>
            						</View>
          					</View>
        				</View>
      			</View>
    		</SafeAreaView>);
};

const styles = StyleSheet.create({
  	fullGamecenter: {
    		flex: 1
  	},
  	viewFrameFlexBox: {
    		alignItems: "center",
    		justifyContent: "center"
  	},
  	parentShadowBox: {
    		paddingBottom: 20,
    		paddingTop: 15,
    		elevation: 8,
    		shadowRadius: 8,
    		shadowColor: "rgba(0, 0, 0, 0.05)",
    		gap: 15,
    		overflow: "hidden",
    		backgroundColor: "#fff",
    		borderRadius: 5,
    		shadowOpacity: 1,
    		shadowOffset: {
      			width: 0,
      			height: 0
    		},
    		alignSelf: "stretch",
    		justifyContent: "center",
    		alignItems: "center"
  	},
  	tue3JunTypo: {
    		textAlign: "center",
    		fontFamily: "Archivo-Regular"
  	},
  	parentFlexBox: {
    		gap: 8,
    		justifyContent: "center",
    		alignItems: "center"
  	},
  	parentFrameFlexBox: {
    		gap: 0,
    		justifyContent: "space-between",
    		flexDirection: "row",
    		alignItems: "center"
  	},
  	textTypo1: {
    		fontSize: 30,
    		color: "#231716",
    		textAlign: "center",
    		fontFamily: "Archivo-Regular"
  	},
  	textTypo: {
    		textAlign: "left",
    		fontFamily: "Archivo-Regular",
    		fontSize: 16
  	},
  	rosterParentFlexBox: {
    		gap: 20,
    		alignItems: "center"
  	},
  	wrapperFlexBox: {
    		overflow: "hidden",
    		alignSelf: "stretch"
  	},
  	oilers1Typo: {
    		fontSize: 14,
    		color: "#000",
    		textAlign: "center"
  	},
  	groupLayout: {
    		height: 23,
    		width: 39,
    		backgroundColor: "#eee",
    		top: 10,
    		position: "absolute"
  	},
  	svTypo: {
    		lineHeight: 16,
    		fontSize: 15,
    		height: 16,
    		color: "#121212",
    		top: 11,
    		position: "absolute",
    		textAlign: "left",
    		fontFamily: "Archivo-Regular"
  	},
  	groupChild2Layout: {
    		width: 29,
    		height: 19,
    		position: "absolute",
    		backgroundColor: "#fff"
  	},
  	groupChildLayout1: {
    		backgroundColor: "#fafafa",
    		width: 29,
    		height: 19,
    		position: "absolute"
  	},
  	groupChildLayout: {
    		width: 6,
    		borderTopWidth: 1,
    		borderColor: "#99989e",
    		left: 233,
    		position: "absolute",
    		height: 1,
    		borderStyle: "solid"
  	},
  	tue3Jun: {
    		color: "#7f7271",
    		fontSize: 16,
    		textAlign: "center",
    		alignSelf: "stretch"
  	},
  	image8Icon: {
    		width: 40,
    		height: 45
  	},
  	panthers: {
    		fontSize: 22,
    		color: "#231716"
  	},
  	image8Parent: {
    		width: 60
  	},
  	text: {
    		width: 23
  	},
  	text1: {
    		width: 14,
    		display: "flex",
    		justifyContent: "center",
    		alignItems: "center"
  	},
  	parent: {
    		width: 160
  	},
  	image9Icon: {
    		width: 45,
    		height: 45
  	},
  	frameParent: {
    		gap: 30,
    		flexDirection: "row",
    		alignSelf: "stretch"
  	},
  	frameChild: {
    		borderColor: "#7f7271",
    		borderTopWidth: 0.5,
    		height: 1,
    		borderStyle: "solid",
    		alignSelf: "stretch"
  	},
  	team: {
    		color: "#7f7271",
    		alignSelf: "stretch"
  	},
  	floridaPanthers: {
    		color: "#231716",
    		alignSelf: "stretch"
  	},
  	teamParent: {
    		gap: 10,
    		justifyContent: "center"
  	},
  	text3: {
    		color: "#7f7271"
  	},
  	text4: {
    		color: "#231716"
  	},
  	group: {
    		gap: 10
  	},
  	frameView: {
    		gap: 10,
    		justifyContent: "center"
  	},
  	frameContainer: {
    		flexDirection: "row"
  	},
  	frameGroup: {
    		paddingHorizontal: 10,
    		paddingVertical: 0,
    		alignSelf: "stretch"
  	},
  	lineParent: {
    		width: 370,
    		paddingTop: 20
  	},
  	tue3JunParent: {
    		paddingHorizontal: 30,
    		gap: 15
  	},
  	roster: {
    		fontSize: 18,
    		color: "#000",
    		fontFamily: "Archivo-Bold",
    		fontWeight: "700",
    		textAlign: "left",
    		alignSelf: "stretch"
  	},
  	oilers1: {
    		fontWeight: "500",
    		fontFamily: "Archivo-Medium"
  	},
  	oilersWrapper: {
    		shadowColor: "rgba(0, 0, 0, 0.15)",
    		shadowRadius: 5,
    		elevation: 5,
    		flexDirection: "row",
    		backgroundColor: "#fff",
    		borderRadius: 5,
    		justifyContent: "center",
    		shadowOpacity: 1,
    		shadowOffset: {
      			width: 0,
      			height: 0
    		},
    		overflow: "hidden",
    		alignItems: "center",
    		flex: 1
  	},
  	panthers1: {
    		fontFamily: "Archivo-Regular",
    		fontSize: 14
  	},
  	panthersWrapper: {
    		flexDirection: "row",
    		borderRadius: 5,
    		overflow: "hidden",
    		justifyContent: "center",
    		alignItems: "center",
    		flex: 1
  	},
  	frameParent2: {
    		borderRadius: 7,
    		backgroundColor: "#e7e7e7",
    		height: 35,
    		padding: 2,
    		flexDirection: "row",
    		justifyContent: "center",
    		alignItems: "center"
  	},
  	screenshot20250609At1052: {
    		maxWidth: "100%",
    		height: 529,
    		borderRadius: 5,
    		overflow: "hidden",
    		width: "100%"
  	},
  	screenshot20250609At10521: {
    		top: 0,
    		left: 0,
    		width: 390,
    		position: "absolute",
    		height: 202,
    		borderRadius: 5
  	},
  	groupChild: {
    		left: 33,
    		width: 83,
    		height: 19,
    		backgroundColor: "#eee",
    		top: 10,
    		position: "absolute"
  	},
  	goalie: {
    		left: 35,
    		lineHeight: 17,
    		width: 67,
    		height: 16,
    		color: "#121212",
    		top: 11,
    		position: "absolute",
    		textAlign: "left",
    		fontFamily: "Archivo-Regular",
    		fontSize: 16
  	},
  	groupItem: {
    		left: 220
  	},
  	groupInner: {
    		left: 304
  	},
  	sv: {
    		left: 219,
    		width: 35
  	},
  	gaa: {
    		left: 306,
    		width: 33
  	},
  	rectangleView: {
    		top: 53,
    		left: 222
  	},
  	groupChild1: {
    		top: 92,
    		left: 221
  	},
  	groupChild2: {
    		top: 131,
    		left: 221
  	},
  	groupChild3: {
    		top: 170,
    		left: 220
  	},
  	lineView: {
    		top: 61
  	},
  	groupChild4: {
    		top: 101
  	},
  	groupChild5: {
    		top: 143
  	},
  	groupChild6: {
    		top: 183
  	},
  	screenshot20250609At1052Group: {
    		height: 202,
    		alignSelf: "stretch"
  	},
  	screenshot20250609At1052Parent: {
    		gap: 5,
    		alignSelf: "stretch"
  	},
  	frameParent1: {
    		gap: 15,
    		alignSelf: "stretch",
    		justifyContent: "center"
  	},
  	rosterParent: {
    		width: 410,
    		padding: 10,
    		backgroundColor: "#fff",
    		borderRadius: 5,
    		justifyContent: "center"
  	},
  	gameInfo: {
    		fontFamily: "Archivo-Bold",
    		fontWeight: "700",
    		textAlign: "left",
    		color: "#231716",
    		fontSize: 16,
    		alignSelf: "stretch"
  	},
  	refereeParent: {
    		width: 87,
    		gap: 15
  	},
  	danialUtegenovParent: {
    		width: 118,
    		gap: 15
  	},
  	frameParent3: {
    		paddingRight: 50,
    		alignSelf: "stretch"
  	},
  	gameInfoParent: {
    		paddingHorizontal: 20,
    		gap: 15
  	},
  	view: {
    		gap: 10,
    		justifyContent: "center",
    		width: "100%",
    		flex: 1
  	}
});

export default FullGamecenter;
